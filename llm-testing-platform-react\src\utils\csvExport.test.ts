import { getExportSummary } from './csvExport';
import type { Question, Model, TestResult, Category } from '../types';

// Mock data for testing
const mockQuestions: Question[] = [
  {
    id: '1',
    title: 'Test Question 1',
    description: 'This is a test question with "quotes" and, commas',
    category: 'CODING'
  },
  {
    id: '2',
    title: 'Test Question 2',
    description: 'Another test question\nwith newlines',
    category: 'LOGIC'
  }
];

const mockModels: Model[] = [
  {
    id: '1',
    name: 'Claude 4 Sonnet',
    provider: 'Anthropic'
  },
  {
    id: '2',
    name: 'ChatGPT 4o',
    provider: 'OpenAI'
  }
];

const mockTestResults: TestResult[] = [
  {
    questionId: '1',
    modelId: '1',
    status: 'passed'
  },
  {
    questionId: '1',
    modelId: '2',
    status: 'failed'
  },
  {
    questionId: '2',
    modelId: '1',
    status: 'pending'
  }
];

const mockCategories: Category[] = [
  {
    id: '1',
    name: 'CODING',
    color: '#667eea',
    description: 'Programming and coding challenges'
  },
  {
    id: '2',
    name: 'LOGIC',
    color: '#764ba2',
    description: 'Logic puzzles and reasoning tasks'
  }
];

describe('CSV Export Utilities', () => {
  describe('getExportSummary', () => {
    it('should calculate correct summary statistics', () => {
      const summary = getExportSummary(mockQuestions, mockModels, mockTestResults);
      
      expect(summary.totalQuestions).toBe(2);
      expect(summary.totalModels).toBe(2);
      expect(summary.totalTests).toBe(4); // 2 questions × 2 models
      expect(summary.completedTests).toBe(2); // 1 passed + 1 failed
      expect(summary.pendingTests).toBe(2); // 4 total - 2 completed
    });

    it('should handle empty data correctly', () => {
      const summary = getExportSummary([], [], []);
      
      expect(summary.totalQuestions).toBe(0);
      expect(summary.totalModels).toBe(0);
      expect(summary.totalTests).toBe(0);
      expect(summary.completedTests).toBe(0);
      expect(summary.pendingTests).toBe(0);
    });
  });
});

// Manual test function for CSV export (can be called from browser console)
(window as any).testCsvExport = () => {
  const { exportTestingGridToCsv } = require('./csvExport');
  exportTestingGridToCsv(mockQuestions, mockModels, mockTestResults, mockCategories);
  console.log('CSV export test completed - check your downloads folder');
};
