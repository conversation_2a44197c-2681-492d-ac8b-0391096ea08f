#!/usr/bin/env python
"""
moving_fractal.py
Generates an animated, non‑periodic, colourful fractal‑like pattern using ModernGL.

Controls
--------
ESC / Q : quit
"""

import sys
import time
import numpy as np
import glfw                           # window & input
import moderngl                       # OpenGL 3.3+ context

# --------------------------------------------------------------------------- #
# 1.  Window / context setup
# --------------------------------------------------------------------------- #
if not glfw.init():
    raise SystemExit("Failed to initialise GLFW")

glfw.window_hint(glfw.CONTEXT_VERSION_MAJOR, 3)
glfw.window_hint(glfw.CONTEXT_VERSION_MINOR, 3)
glfw.window_hint(glfw.OPENGL_PROFILE, glfw.OPENGL_CORE_PROFILE)
glfw.window_hint(glfw.DOUBLEBUFFER, 1)

width, height = 1280, 720
window = glfw.create_window(width, height, "ModernGL – Moving Fractal", None, None)
if not window:
    glfw.terminate()
    raise SystemExit("Failed to create window")

glfw.make_context_current(window)
ctx = moderngl.create_context()
ctx.enable(moderngl.BLEND)            # allows future transparency tricks

# --------------------------------------------------------------------------- #
# 2.  Full‑screen quad
# --------------------------------------------------------------------------- #
quad = ctx.buffer(np.array([
    -1.0, -1.0, 0.0, 0.0,   # (x, y, u, v)
     1.0, -1.0, 1.0, 0.0,
    -1.0,  1.0, 0.0, 1.0,
     1.0,  1.0, 1.0, 1.0
], dtype="f4"))

vao = ctx.simple_vertex_array(
    ctx.program(
        vertex_shader="""
            #version 330 core
            in  vec2 in_vert;
            in  vec2 in_uv;
            out vec2 v_uv;
            void main() {
                v_uv = in_uv;
                gl_Position = vec4(in_vert, 0.0, 1.0);
            }
        """,
        fragment_shader="""
            #version 330 core

            /* ---------- Uniforms ---------- */
            uniform float iTime;
            uniform vec2  iResolution;

            /* ---------- Varyings ---------- */
            in  vec2 v_uv;
            out vec4 fragColor;

            /* ---------- Hash‑based pseudo‑random value --------------- */
            float hash21(vec2 p) {
                p = fract(p * vec2(123.34, 456.21));
                p += dot(p, p + 45.32);
                return fract(p.x * p.y);
            }

            /* ---------- 2‑D value noise -------------------------------- */
            float vnoise(vec2 p) {
                vec2 i = floor(p);
                vec2 f = fract(p);

                /* 2×2 hash grid */
                float a = hash21(i);
                float b = hash21(i + vec2(1.0, 0.0));
                float c = hash21(i + vec2(0.0, 1.0));
                float d = hash21(i + vec2(1.0, 1.0));

                /* smooth Hermite curve (ease) */
                vec2 u = f * f * (3.0 - 2.0 * f);

                /* bilinear interpolation */
                return mix(mix(a, b, u.x), mix(c, d, u.x), u.y);
            }

            /* ---------- Fractal Brownian Motion ----------------------- */
            float fbm(vec2 p) {
                float value = 0.0;
                float amp   = 0.5;
                mat2  rot   = mat2( 0.80,  0.60,
                                   -0.60,  0.80);  // domain warp to kill repetition

                for (int i = 0; i < 6; i++) {
                    value += amp * vnoise(p);
                    p = rot * p * 2.1;   // rotate & scale -> quasi‑chaotic
                    amp *= 0.55;         // diminishing amplitudes
                }
                return value;
            }

            void main() {
                /* Normalised pixel coords, centred, aspect‑correct */
                vec2 uv = (v_uv * 2.0 - 1.0);
                uv.x *= iResolution.x / iResolution.y;

                /* Animated coordinate space */
                float t = iTime * 0.25;
                vec2 p  = uv * 3.0 + vec2(t * 0.7, t * 0.4);

                /* Two layered fBm fields with different warps */
                float n1 = fbm(p);
                float n2 = fbm(p.yx + 4.0);

                /* Palette: classic sine‑wave (!) */
                vec3 col = 0.5 + 0.5 * cos(6.28318 * (n1 + n2) + vec3(0.0, 0.33, 0.67));

                /* Extra contrast & saturation */
                col = pow(col, vec3(1.4));

                fragColor = vec4(col, 1.0);
            }
        """
    ),
    quad, "in_vert", "in_uv"
)

# Grab uniform locations once
iTime_loc       = vao.program["iTime"]
iResolution_loc = vao.program["iResolution"]

# --------------------------------------------------------------------------- #
# 3.  Render loop
# --------------------------------------------------------------------------- #
start_time = time.perf_counter()

while not glfw.window_should_close(window):
    glfw.poll_events()

    # Handle ESC / Q key quickly
    if glfw.get_key(window, glfw.KEY_ESCAPE) == glfw.PRESS or \
       glfw.get_key(window, glfw.KEY_Q) == glfw.PRESS:
        glfw.set_window_should_close(window, True)

    # Window can be resized at runtime
    new_w, new_h = glfw.get_framebuffer_size(window)
    if (new_w, new_h) != (width, height):
        width, height = new_w, new_h
        ctx.viewport = (0, 0, width, height)

    # --- Clear & draw full‑screen quad ---
    ctx.clear(0.0, 0.0, 0.0, 1.0)
    iTime_loc.value       = time.perf_counter() - start_time
    iResolution_loc.value = (width, height)
    vao.render(moderngl.TRIANGLE_STRIP)

    # Swap buffers
    glfw.swap_buffers(window)

# --------------------------------------------------------------------------- #
# 4.  Shutdown
# --------------------------------------------------------------------------- #
glfw.terminate()
sys.exit(0)
