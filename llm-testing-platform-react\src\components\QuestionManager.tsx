import React, { useState, useEffect } from 'react';
import type { Question, Category } from '../types';
import { saveQuestions, getCategories } from '../utils/storage';

interface QuestionManagerProps {
  questions: Question[];
  onQuestionsChange: (questions: Question[]) => void;
}

const QuestionManager: React.FC<QuestionManagerProps> = ({
  questions,
  onQuestionsChange
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [categories, setCategories] = useState<Category[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'CODING'
  });

  useEffect(() => {
    setCategories(getCategories());
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingId) {
      // Edit existing question
      const updatedQuestions = questions.map(q => 
        q.id === editingId 
          ? { ...q, ...formData }
          : q
      );
      onQuestionsChange(updatedQuestions);
      saveQuestions(updatedQuestions);
      setEditingId(null);
    } else {
      // Add new question
      const newQuestion: Question = {
        id: Date.now().toString(),
        ...formData
      };
      const updatedQuestions = [...questions, newQuestion];
      onQuestionsChange(updatedQuestions);
      saveQuestions(updatedQuestions);
      setIsAdding(false);
    }
    
    setFormData({ title: '', description: '', category: 'CODING' });
  };

  const handleEdit = (question: Question) => {
    setFormData({
      title: question.title,
      description: question.description,
      category: question.category
    });
    setEditingId(question.id);
    setIsAdding(true);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this question?')) {
      const updatedQuestions = questions.filter(q => q.id !== id);
      onQuestionsChange(updatedQuestions);
      saveQuestions(updatedQuestions);
    }
  };

  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setFormData({ title: '', description: '', category: 'CODING' });
  };

  const handleCopyQuestion = async (question: Question) => {
    const textToCopy = question.description;

    try {
      await navigator.clipboard.writeText(textToCopy);
      // You could add a toast notification here
      alert('Question description copied to clipboard!');
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = textToCopy;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Question description copied to clipboard!');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold gradient-text float-animation">
          ❓ Question Management
        </h2>
        <button
          onClick={() => setIsAdding(true)}
          className="btn-animated bg-gradient-to-r from-green-500 to-teal-500 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          ✨ Add Question
        </button>
      </div>

      {isAdding && (
        <div className="glass-effect rounded-xl p-6 card-animated">
          <h3 className="text-xl font-semibold mb-4 text-white">
            {editingId ? '✏️ Edit Question' : '➕ Add New Question'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-white font-medium mb-2">
                Title
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white placeholder-white/70 border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
                placeholder="Enter question title..."
                required
              />
            </div>
            <div>
              <label className="block text-white font-medium mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={4}
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white placeholder-white/70 border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
                placeholder="Enter question description..."
                required
              />
            </div>
            <div>
              <label className="block text-white font-medium mb-2">
                Category
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.name} className="bg-gray-800 text-white">
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex gap-3">
              <button
                type="submit"
                className="btn-animated bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-lg font-semibold flex-1"
              >
                {editingId ? '💾 Update Question' : '✅ Add Question'}
              </button>
              <button
                type="button"
                onClick={handleCancel}
                className="btn-animated bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-3 rounded-lg font-semibold"
              >
                ❌ Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {questions.map((question, index) => {
          const category = categories.find(cat => cat.name === question.category);
          return (
            <div
              key={question.id}
              className="glass-effect rounded-xl p-6 card-animated hover:scale-105 transition-all duration-300"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div
                    className="w-4 h-4 rounded-full pulse-animation"
                    style={{ backgroundColor: category?.color || '#667eea' }}
                  />
                  <span className="text-white/80 text-sm font-medium">
                    {question.category}
                  </span>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleCopyQuestion(question)}
                    className="btn-animated bg-gradient-to-r from-green-500 to-teal-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                    title="Copy question description"
                  >
                    📋
                  </button>
                  <button
                    onClick={() => handleEdit(question)}
                    className="btn-animated bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                  >
                    ✏️
                  </button>
                  <button
                    onClick={() => handleDelete(question.id)}
                    className="btn-animated bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                  >
                    🗑️
                  </button>
                </div>
              </div>

              <div className="flex items-center gap-2 mb-3">
                <h4 className="text-xl font-bold text-white break-words">
                  {question.title}
                </h4>
              </div>
              <p className="text-white/80 text-sm leading-relaxed break-words whitespace-pre-wrap">
                {question.description}
              </p>
            </div>
          );
        })}
      </div>

      {questions.length === 0 && !isAdding && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4 bounce-animation">❓</div>
          <h3 className="text-2xl font-bold text-white mb-2">No Questions Yet</h3>
          <p className="text-white/70 mb-6">Create your first question to start testing!</p>
          <button
            onClick={() => setIsAdding(true)}
            className="btn-animated bg-gradient-to-r from-green-500 to-teal-500 text-white px-8 py-4 rounded-lg font-semibold text-lg"
          >
            🚀 Create First Question
          </button>
        </div>
      )}
    </div>
  );
};

export default QuestionManager;
