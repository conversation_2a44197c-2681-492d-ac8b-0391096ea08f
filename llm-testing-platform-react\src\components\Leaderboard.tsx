import React from 'react';
import type { ModelScore } from '../types';

interface LeaderboardProps {
  scores: ModelScore[];
}

const Leaderboard: React.FC<LeaderboardProps> = ({ scores }) => {
  const getRankColor = (rank: number): string => {
    switch (rank) {
      case 1: return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white shadow-lg scale-110';
      case 2: return 'bg-gradient-to-r from-gray-400 to-gray-600 text-white shadow-lg scale-105';
      case 3: return 'bg-gradient-to-r from-orange-400 to-orange-600 text-white shadow-lg scale-102';
      default: return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white';
    }
  };

  const getRankIcon = (rank: number): string => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  const getPassRateColor = (passRate: number): string => {
    if (passRate >= 80) return 'bg-gradient-to-r from-green-500 to-emerald-500 text-white';
    if (passRate >= 60) return 'bg-gradient-to-r from-yellow-500 to-amber-500 text-white';
    if (passRate >= 40) return 'bg-gradient-to-r from-orange-500 to-red-500 text-white';
    return 'bg-gradient-to-r from-red-500 to-pink-500 text-white';
  };

  if (scores.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4 bounce-animation">🏆</div>
        <h3 className="text-2xl font-bold text-white mb-2">No Results Yet</h3>
        <p className="text-white/70 text-lg">
          Start testing models to see the leaderboard!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold gradient-text float-animation">
          🏆 Leaderboard
        </h2>
        <div className="text-white/80 font-medium">
          📊 Ranked by pass rate
        </div>
      </div>

      <div className="glass-effect rounded-xl overflow-hidden card-animated">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-bold text-white uppercase tracking-wider">
                  🏅 Rank
                </th>
                <th className="px-6 py-4 text-left text-sm font-bold text-white uppercase tracking-wider">
                  🤖 Model
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  📈 Pass Rate
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  ✅ Passed
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  ❌ Failed
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  ⏳ Pending
                </th>
                <th className="px-6 py-4 text-center text-sm font-bold text-white uppercase tracking-wider">
                  📊 Total
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-white/10">
              {scores.map((score, index) => {
                const rank = index + 1;
                return (
                  <tr key={score.modelId} className="hover:bg-white/5 transition-all duration-300">
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold transition-all duration-300 ${getRankColor(rank)} ${rank <= 3 ? 'pulse-animation' : ''}`}>
                        {getRankIcon(rank)}
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className="card-animated flex items-center gap-3" style={{ animationDelay: `${index * 0.1}s` }}>
                        <div className="flex-shrink-0">
                          {score.imageUrl ? (
                            <img
                              src={score.imageUrl}
                              alt={score.modelName}
                              className="w-10 h-10 rounded-lg object-cover"
                              onError={(e) => {
                                // Fallback to icon or default if image fails to load
                                e.currentTarget.style.display = 'none';
                                const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                                if (nextElement) nextElement.style.display = 'block';
                              }}
                            />
                          ) : null}
                          <span className="text-2xl" style={{ display: score.imageUrl ? 'none' : 'block' }}>
                            {score.icon || '🤖'}
                          </span>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white">
                            {score.modelName}
                          </div>
                          <div className="text-white/70">
                            {score.provider}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <div className={`inline-flex items-center px-4 py-2 rounded-xl text-xl font-bold transition-all duration-300 ${getPassRateColor(score.passRate)} ${rank === 1 ? 'pulse-animation' : ''}`}>
                        {score.passRate.toFixed(1)}%
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-green-500 to-emerald-500 text-white">
                        {score.passedQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-red-500 to-pink-500 text-white">
                        {score.failedQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center">
                      <span className="inline-flex items-center px-3 py-2 rounded-lg text-sm font-bold bg-gradient-to-r from-gray-500 to-gray-600 text-white">
                        {score.pendingQuestions}
                      </span>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap text-center text-lg font-bold text-white">
                      {score.totalQuestions}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      <div className="glass-effect rounded-xl p-6 card-animated">
        <div className="flex items-start gap-4">
          <div className="text-3xl">💡</div>
          <div>
            <h3 className="text-lg font-bold text-white mb-2">
              How the leaderboard works
            </h3>
            <p className="text-white/80 leading-relaxed">
              Models are ranked by their pass rate (percentage of questions passed).
              The pass rate is calculated as: <span className="font-bold text-white">(Passed Questions ÷ Total Questions) × 100%</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Leaderboard;
