import React, { useState, useEffect } from 'react';
import { Category } from '../types';
import { getCategories, saveCategories } from '../utils/storage';

const CategoryManager: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [newCategory, setNewCategory] = useState({
    name: '',
    color: '#667eea',
    description: ''
  });

  const colorOptions = [
    '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe',
    '#10b981', '#ef4444', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16'
  ];

  useEffect(() => {
    setCategories(getCategories());
  }, []);

  const handleAddCategory = () => {
    if (newCategory.name.trim()) {
      const category: Category = {
        id: Date.now().toString(),
        name: newCategory.name.trim(),
        color: newCategory.color,
        description: newCategory.description.trim() || undefined
      };
      
      const updatedCategories = [...categories, category];
      setCategories(updatedCategories);
      saveCategories(updatedCategories);
      
      setNewCategory({ name: '', color: '#667eea', description: '' });
      setIsAddingCategory(false);
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setNewCategory({
      name: category.name,
      color: category.color,
      description: category.description || ''
    });
  };

  const handleUpdateCategory = () => {
    if (editingCategory && newCategory.name.trim()) {
      const updatedCategories = categories.map(cat =>
        cat.id === editingCategory.id
          ? {
              ...cat,
              name: newCategory.name.trim(),
              color: newCategory.color,
              description: newCategory.description.trim() || undefined
            }
          : cat
      );
      
      setCategories(updatedCategories);
      saveCategories(updatedCategories);
      
      setEditingCategory(null);
      setNewCategory({ name: '', color: '#667eea', description: '' });
    }
  };

  const handleDeleteCategory = (categoryId: string) => {
    if (confirm('Are you sure you want to delete this category?')) {
      const updatedCategories = categories.filter(cat => cat.id !== categoryId);
      setCategories(updatedCategories);
      saveCategories(updatedCategories);
    }
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setIsAddingCategory(false);
    setNewCategory({ name: '', color: '#667eea', description: '' });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-3xl font-bold gradient-text float-animation">
          🏷️ Category Manager
        </h2>
        <button
          onClick={() => setIsAddingCategory(true)}
          className="btn-animated bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
        >
          ✨ Add Category
        </button>
      </div>

      {/* Add/Edit Category Form */}
      {(isAddingCategory || editingCategory) && (
        <div className="glass-effect rounded-xl p-6 card-animated">
          <h3 className="text-xl font-semibold mb-4 text-white">
            {editingCategory ? '✏️ Edit Category' : '➕ Add New Category'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white font-medium mb-2">Category Name</label>
              <input
                type="text"
                value={newCategory.name}
                onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white placeholder-white/70 border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
                placeholder="Enter category name..."
              />
            </div>
            
            <div>
              <label className="block text-white font-medium mb-2">Color</label>
              <div className="flex flex-wrap gap-2">
                {colorOptions.map(color => (
                  <button
                    key={color}
                    onClick={() => setNewCategory({ ...newCategory, color })}
                    className={`w-8 h-8 rounded-full border-2 transition-all duration-300 hover:scale-110 ${
                      newCategory.color === color ? 'border-white scale-110' : 'border-white/30'
                    }`}
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
            
            <div className="md:col-span-2">
              <label className="block text-white font-medium mb-2">Description (Optional)</label>
              <textarea
                value={newCategory.description}
                onChange={(e) => setNewCategory({ ...newCategory, description: e.target.value })}
                className="w-full px-4 py-3 rounded-lg bg-white/20 text-white placeholder-white/70 border border-white/30 focus:border-white/60 focus:outline-none transition-all duration-300"
                placeholder="Enter category description..."
                rows={3}
              />
            </div>
          </div>
          
          <div className="flex gap-3 mt-6">
            <button
              onClick={editingCategory ? handleUpdateCategory : handleAddCategory}
              className="btn-animated bg-gradient-to-r from-green-500 to-blue-500 text-white px-6 py-3 rounded-lg font-semibold flex-1"
            >
              {editingCategory ? '💾 Update Category' : '✅ Add Category'}
            </button>
            <button
              onClick={cancelEdit}
              className="btn-animated bg-gradient-to-r from-gray-500 to-gray-600 text-white px-6 py-3 rounded-lg font-semibold"
            >
              ❌ Cancel
            </button>
          </div>
        </div>
      )}

      {/* Categories List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category, index) => (
          <div
            key={category.id}
            className="glass-effect rounded-xl p-6 card-animated hover:scale-105 transition-all duration-300"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div
                  className="w-6 h-6 rounded-full pulse-animation"
                  style={{ backgroundColor: category.color }}
                />
                <h3 className="text-xl font-bold text-white">{category.name}</h3>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => handleEditCategory(category)}
                  className="btn-animated bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                >
                  ✏️
                </button>
                <button
                  onClick={() => handleDeleteCategory(category.id)}
                  className="btn-animated bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-2 rounded-lg text-sm hover:shadow-lg"
                >
                  🗑️
                </button>
              </div>
            </div>
            
            {category.description && (
              <p className="text-white/80 text-sm">{category.description}</p>
            )}
          </div>
        ))}
      </div>

      {categories.length === 0 && !isAddingCategory && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4 bounce-animation">🏷️</div>
          <h3 className="text-2xl font-bold text-white mb-2">No Categories Yet</h3>
          <p className="text-white/70 mb-6">Create your first category to organize your questions!</p>
          <button
            onClick={() => setIsAddingCategory(true)}
            className="btn-animated bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-lg font-semibold text-lg"
          >
            🚀 Create First Category
          </button>
        </div>
      )}
    </div>
  );
};

export default CategoryManager;
