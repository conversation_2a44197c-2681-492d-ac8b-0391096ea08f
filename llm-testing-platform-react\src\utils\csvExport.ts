import type { Question, Model, TestResult, Category } from '../types';

/**
 * Escapes CSV field values to handle commas, quotes, and newlines
 */
const escapeCsvField = (field: string): string => {
  if (field.includes(',') || field.includes('"') || field.includes('\n') || field.includes('\r')) {
    // Escape quotes by doubling them and wrap the entire field in quotes
    return `"${field.replace(/"/g, '""')}"`;
  }
  return field;
};

/**
 * Gets the test result status for a specific question and model combination
 */
const getResultStatus = (questionId: string, modelId: string, testResults: TestResult[]): string => {
  const result = testResults.find(r => r.questionId === questionId && r.modelId === modelId);
  return result?.status || 'pending';
};

/**
 * Exports the testing grid data to CSV format
 */
export const exportTestingGridToCsv = (
  questions: Question[],
  models: Model[],
  testResults: TestResult[],
  categories: Category[]
): void => {
  // Create CSV header
  const headers = [
    'Question Title',
    'Question Description', 
    'Category',
    ...models.map(model => `${model.name} (${model.provider})`)
  ];

  // Create CSV rows
  const rows: string[][] = [];
  
  // Add header row
  rows.push(headers);

  // Add data rows
  questions.forEach(question => {
    const category = categories.find(cat => cat.name === question.category);
    const row = [
      question.title,
      question.description,
      question.category,
      ...models.map(model => {
        const status = getResultStatus(question.id, model.id, testResults);
        // Convert status to more readable format
        switch (status) {
          case 'passed': return 'Passed';
          case 'failed': return 'Failed';
          default: return 'Pending';
        }
      })
    ];
    rows.push(row);
  });

  // Convert to CSV string
  const csvContent = rows
    .map(row => row.map(field => escapeCsvField(field)).join(','))
    .join('\n');

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    // Create download link
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    
    // Generate filename with current date
    const now = new Date();
    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
    const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS format
    link.setAttribute('download', `llm-testing-results-${dateStr}-${timeStr}.csv`);
    
    // Trigger download
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    URL.revokeObjectURL(url);
  }
};

/**
 * Gets summary statistics for the export
 */
export const getExportSummary = (
  questions: Question[],
  models: Model[],
  testResults: TestResult[]
): {
  totalQuestions: number;
  totalModels: number;
  totalTests: number;
  completedTests: number;
  pendingTests: number;
} => {
  const totalQuestions = questions.length;
  const totalModels = models.length;
  const totalTests = totalQuestions * totalModels;
  const completedTests = testResults.filter(r => r.status !== 'pending').length;
  const pendingTests = totalTests - completedTests;

  return {
    totalQuestions,
    totalModels,
    totalTests,
    completedTests,
    pendingTests
  };
};
